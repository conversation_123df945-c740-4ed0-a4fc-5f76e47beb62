<div>
    <div class="row" style="">
        <div class="col-lg-12">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>
                        <span><vc:i18n name="查询条件" namespace="inspectionTaskDetails"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;">
                        <button type="button" class="btn btn-link btn-sm" style="margin-right:10px;"
                                v-on:click="_moreCondition()">
                            {{inspectionTaskDetailManageInfo.moreCondition == true?'隐藏':'更多'}}
                        </button>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入巡检人','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.planUserName"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检开始时间','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.inspectionStartTime"
                                       class=" form-control startTime">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检结束时间','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.inspectionEndTime"
                                       class=" form-control endTime">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_queryInspectionTaskMethod()">
                                <i class="fa fa-search"></i>
                                <span><vc:i18n name="查询" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_resetInspectionTaskMethod()">
                                <i class="fa fa-repeat"></i>
                                <span><vc:i18n name="重置" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </button>
                        </div>
                    </div>
                    <div class="row" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionPlanId">
                                <option selected value="">{{vc.i18n('请选择巡检计划','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionPlanList"
                                        :key="index" :value="item.inspectionPlanId">
                                    {{item.inspectionPlanName}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionRouteId">
                                <option selected value="">{{vc.i18n('请选择巡检路线','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionRouteList"
                                        :key="index" :value="item.inspectionRouteId">
                                    {{item.routeName}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionId">
                                <option selected value="">{{vc.i18n('请选择巡检点','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionPointList"
                                        :key="index" :value="item.inspectionId">
                                    {{item.inspectionName}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row margin-top" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入任务详情ID','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.taskDetailId"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionState">
                                <option selected value="">{{vc.i18n('请选择签到状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionStateTypes"
                                        :key="index" :value="item.statusCd">
                                    {{item.name}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <select class="custom-select" v-model="inspectionTaskDetailManageInfo.conditions.state">
                                <option selected value="">{{vc.i18n('请选择巡检点状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.stateTypes" :key="index"
                                        :value="item.statusCd">
                                    {{item.name}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <select class="custom-select" v-model="inspectionTaskDetailManageInfo.conditions.taskState">
                                <option selected value="">{{vc.i18n('请选择任务状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.taskStates" :key="index"
                                        :value="item.statusCd">{{item.name}}
                                </option>
                            </select>
                        </div>
                        <!--<div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.patrolType">
                                <option selected value="">{{vc.i18n('请选择巡检情况','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.patrolTypes" :key="index"
                                        :value="item.statusCd">{{item.name}}
                                </option>
                            </select>
                        </div>-->
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.resultFlag">
                                <option selected value="">{{vc.i18n('请选择巡检结果','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionResult" :key="index"
                                        :value="item.value">{{item.name}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row" style="">
        <div class="col-lg-12" >
            <div class="ibox" >
                <div class="ibox-title" >
                    <h5>
                        <span><vc:i18n name="巡检明细信息" namespace="inspectionTaskDetails"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;">
                        <button type="button" class="btn btn-primary btn-sm" v-on:click="_exportExcel()">
                            <i class="fa fa-plus"></i>
                            <span><vc:i18n name="导出" namespace="inspectionTaskDetails"></vc:i18n></span>
                        </button>
                    </div>
                </div>
                <div class="ibox-content">
                    <el-table
                            :data="inspectionTaskDetailManageInfo.inspectionTasks"
                            style="width: 100%"
                            @sort-change="sortTable"
                            class="clickable-rows inspection-detail-table"
                    >
                        <!--<el-table-column
                                prop="taskDetailId"
                                label="任务详情ID"
                                width="180">
                        </el-table-column>-->
                        <el-table-column
                                prop="resultFlag"
                                label="巡检结果"
                                width="80">
                            <template slot-scope="scope">
                                <span v-if="scope.row.resultFlag == 1" style="color: green">正常</span>
                                <span v-else-if="scope.row.resultFlag == 2" style="color: red">异常</span>
                                <span v-else >暂无结果</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="inspectionName"
                                label="巡检点"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="inspectionPlanName"
                                label="巡检计划"
                                width="150">
                        </el-table-column>
                        <el-table-column
                                prop="routeName"
                                label="巡检路线"
                                width="120">
                        </el-table-column>
                        <el-table-column
                                prop="planInsTime"
                                label="巡检人开始/结束时间"
                                width="200"
                                sortable="custom"
                        >
                            <template slot-scope="scope">
                                <span>{{ scope.row.planInsTime }}<br/>{{ scope.row.planEndTime }}</span>
                            </template>
                        </el-table-column>

                        <!--<el-table-column
                                prop="pointStartTime"
                                label="巡检点开始/结束时间"
                                width="200"
                                sortable="custom"
                        >
                            <template slot-scope="scope">
                                <span>{{ scope.row.pointStartTime }}<br/>{{ scope.row.pointEndTime }}</span>
                            </template>
                        </el-table-column>-->
                        <el-table-column
                                prop="inspectionTime"
                                label="巡检时间"
                                width="150"
                                sortable="custom"
                        >
                            <template slot-scope="scope">
                                <span >{{ scope.row.inspectionTime ? scope.row.inspectionTime : '-' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="inspectionState"
                                label="签到状态"
                                width="100"
                        >
                            <template slot-scope="scope">
                                <span class="text-center text-primary" v-if="scope.row.inspectionState == '60000'">
                                    {{scope.row.inspectionStateName ? scope.row.inspectionStateName : '-'}}
                                </span>
                                <span class="text-center text-danger font-bold" v-else>{{scope.row.inspectionStateName ?
                                    scope.row.inspectionStateName : '-'}}
                                </span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="planUserName"
                                label="计划巡检人"
                                width="120"
                        >
                        </el-table-column>

                        <el-table-column
                                prop="actUserName"
                                label="实际巡检人"
                                width="120"
                        >
                            <template slot-scope="scope">
                                <span style="">{{ scope.row.actUserName ? scope.row.actUserName : '-' }}</span>
                            </template>
                        </el-table-column>

                       <!-- <el-table-column
                                prop="signTypeName"
                                label="巡检方式"
                        >
                        </el-table-column>-->

                        <el-table-column
                                prop="taskStateName"
                                label="任务状态"
                                width="100"
                        >
                        </el-table-column>

                       <!-- <el-table-column
                                prop="stateName"
                                label="巡检点状态"
                        >
                            <template slot-scope="scope">
                                <span v-if="scope.row.stateName == '20200408'" class="text-center text-danger font-bold">{{ scope.row.stateName }}</span>
                                <span v-else class="text-center">{{ scope.row.stateName }}</span>
                            </template>
                        </el-table-column>-->

                        <!--<el-table-column
                                prop="signTypeName"
                                label="巡检情况"
                        >
                            <template slot-scope="scope">
                                <span style="">{{ scope.row.description ? scope.row.description : '-' }}</span>
                            </template>
                        </el-table-column>-->

                        <el-table-column
                                prop="signTypeName"
                                label="巡检照片"
                                width="120"
                        >
                            <template slot-scope="scope">
                                <span v-for="_photo in scope.row.photos" :key="_photo.url" style="margin-right: 5px;">
                                    <img style="width: 50px; height: 50px; cursor: pointer; border-radius: 4px;" v-bind:src="_photo.url" v-on:click="openFile(_photo)"/>
                                </span>
                                <span v-if="!scope.row.photos || scope.row.photos.length === 0" style="color: #999;">无照片</span>
                            </template>
                        </el-table-column>
                        <el-table-column
                                prop="inspectionPlanPeriodName"
                                label="操作"
                                fixed="right"
                                width="100"
                        >
                            <template slot-scope="scope">
                                <el-button  @click="_viewInspectionTaskDetail(scope.row)" type="text" size="small">详情</el-button>
                            </template>
                        </el-table-column>

                        <!--<el-table-column
                                prop="createTime"
                                label="创建时间"
                        >
                        </el-table-column>-->

                    </el-table>
                    <!--<table class="footable table table-stripped toggle-arrow-tiny" data-page-size="15" style="overflow-x: scroll">
                        <thead>
                        <tr>
                            <th class="text-center">
                                <span><vc:i18n name="任务详情ID" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检计划名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检路线名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检人" namespace="inspectionTaskDetails"></vc:i18n></span><br/>
                                <span><vc:i18n name="开始/结束时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点" namespace="inspectionTaskDetails"></vc:i18n></span><br/>
                                <span><vc:i18n name="开始/结束时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际巡检时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际签到状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="计划巡检人" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际巡检人" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检方式" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="任务状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center" style="width: 200px;">
                                <span><vc:i18n name="巡检情况" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检照片" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="创建时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            &lt;!&ndash;<th class="text-center" style="width: 70px;">
                                <span><vc:i18n name="位置信息" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>&ndash;&gt;
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="inspectionTask in inspectionTaskDetailManageInfo.inspectionTasks">
                            <td class="text-center">{{inspectionTask.taskDetailId}}</td>
                            <td class="text-center">{{inspectionTask.inspectionName}}</td>
                            <td class="text-center">{{inspectionTask.inspectionPlanName}}</td>
                            <td class="text-center">{{inspectionTask.routeName}}</td>
                            <td class="text-center">{{inspectionTask.planInsTime}}<br/>{{inspectionTask.planEndTime}}
                            </td>
                            <td class="text-center">{{inspectionTask.pointStartTime}}<br/>{{inspectionTask.pointEndTime}}
                            </td>
                            <td class="text-center">{{inspectionTask.inspectionTime ? inspectionTask.inspectionTime :
                                '-'}}
                            </td>
                            <td class="text-center text-primary" v-if="inspectionTask.inspectionState == '60000'">
                                {{inspectionTask.inspectionStateName ? inspectionTask.inspectionStateName : '-'}}
                            </td>
                            <td class="text-center text-danger font-bold" v-else>{{inspectionTask.inspectionStateName ?
                                inspectionTask.inspectionStateName : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.planUserName}}</td>
                            <td class="text-center">{{inspectionTask.actUserName ? inspectionTask.actUserName : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.signTypeName}}</td>
                            <td class="text-center">{{inspectionTask.taskStateName}}</td>
                            <td class="text-center text-danger font-bold" v-if="inspectionTask.state == '20200408'">
                                {{inspectionTask.stateName}}
                            </td>
                            <td class="text-center" v-else>{{inspectionTask.stateName}}</td>
                            &lt;!&ndash; <td class="text-center text-danger font-bold" v-if="inspectionTask.patrolType == '20002'">{{inspectionTask.patrolTypeName ? inspectionTask.patrolTypeName : '-'}}</td>
                            <td class="text-center text-primary" v-else>{{inspectionTask.patrolTypeName ? inspectionTask.patrolTypeName : '-'}}</td> &ndash;&gt;
                            <td class="text-center text-primary" style="word-break: break-all">
                                {{inspectionTask.description ? inspectionTask.description : '-'}}
                            </td>
                            <td class="text-center" style="white-space: nowrap;">
                                    <span v-for="_photo in inspectionTask.photos">
                                    <img style="width: 60px; height: 60px;" v-bind:src="_photo.url"
                                         v-on:click="openFile(_photo)"/>
                                </span>
                            </td>
                            <td class="text-center">{{inspectionTask.createTime}}</td>
                            &lt;!&ndash;<td class="text-center">
                                <button class="btn btn-info"
                                        v-on:click="openMap(inspectionTask.latitude, inspectionTask.longitude)">
                                    <span><vc:i18n name="查看" namespace="inspectionTaskDetails"></vc:i18n></span>
                                </button>
                            </td>&ndash;&gt;
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="17">
                                <ul class="pagination float-right"></ul>
                            </td>
                        </tr>
                        </tfoot>
                    </table>-->
                    <!-- 分页 -->
                    <vc:create path="frame/pagination"></vc:create>
                </div>
            </div>
        </div>
    </div>
    <vc:create path="common/viewMap"></vc:create>
    <vc:create path="common/viewImage"></vc:create>
</div>

<style>
/* 巡检明细表格样式优化 */
.inspection-detail-table {
    min-width: 1300px; /* 设置表格最小宽度，确保所有列都能显示 */
}

.inspection-detail-table .el-table__body-wrapper {
    overflow-x: auto; /* 启用水平滚动 */
}

/* 确保操作列始终可见 */
.inspection-detail-table .el-table__fixed-right {
    box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1); /* 添加阴影效果 */
}

/* 优化操作按钮的间距 */
.inspection-detail-table .el-button + .el-button {
    margin-left: 5px;
}

/* 巡检照片样式优化 */
.inspection-detail-table img {
    transition: transform 0.2s ease;
}

.inspection-detail-table img:hover {
    transform: scale(1.1);
}

/* 响应式处理 */
@media (max-width: 768px) {
    .inspection-detail-table {
        font-size: 12px; /* 在小屏幕上减小字体 */
    }

    .inspection-detail-table .el-button--small {
        padding: 5px 8px; /* 减小按钮内边距 */
        font-size: 11px;
    }

    .inspection-detail-table img {
        width: 40px !important;
        height: 40px !important;
    }
}

/* 确保表格容器可以水平滚动 */
.ibox-content {
    overflow-x: auto;
}
</style>
</div>
