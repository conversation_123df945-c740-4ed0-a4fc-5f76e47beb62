<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>
                        <span><vc:i18n name="查询条件" namespace="inspectionTaskDetails"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;">
                        <button type="button" class="btn btn-link btn-sm" style="margin-right:10px;"
                                v-on:click="_moreCondition()">
                            {{inspectionTaskDetailManageInfo.moreCondition == true?'隐藏':'更多'}}
                        </button>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入巡检人','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.planUserName"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检开始时间','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.inspectionStartTime"
                                       class=" form-control startTime">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检结束时间','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.inspectionEndTime"
                                       class=" form-control endTime">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_queryInspectionTaskMethod()">
                                <i class="fa fa-search"></i>
                                <span><vc:i18n name="查询" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_resetInspectionTaskMethod()">
                                <i class="fa fa-repeat"></i>
                                <span><vc:i18n name="重置" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </button>
                        </div>
                    </div>
                    <div class="row" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionPlanId">
                                <option selected value="">{{vc.i18n('请选择巡检计划','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionPlanList"
                                        :key="index" :value="item.inspectionPlanId">
                                    {{item.inspectionPlanName}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionRouteId">
                                <option selected value="">{{vc.i18n('请选择巡检路线','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionRouteList"
                                        :key="index" :value="item.inspectionRouteId">
                                    {{item.routeName}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionId">
                                <option selected value="">{{vc.i18n('请选择巡检点','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionPointList"
                                        :key="index" :value="item.inspectionId">
                                    {{item.inspectionName}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row margin-top" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入任务详情ID','inspectionTaskDetails')"
                                       v-model="inspectionTaskDetailManageInfo.conditions.taskDetailId"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.inspectionState">
                                <option selected value="">{{vc.i18n('请选择签到状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.inspectionStateTypes"
                                        :key="index" :value="item.statusCd">
                                    {{item.name}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-3">
                            <select class="custom-select" v-model="inspectionTaskDetailManageInfo.conditions.state">
                                <option selected value="">{{vc.i18n('请选择巡检点状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.stateTypes" :key="index"
                                        :value="item.statusCd">
                                    {{item.name}}
                                </option>
                            </select>
                        </div>
                    </div>
                    <div class="row" v-show="inspectionTaskDetailManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <select class="custom-select" v-model="inspectionTaskDetailManageInfo.conditions.taskState">
                                <option selected value="">{{vc.i18n('请选择任务状态','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.taskStates" :key="index"
                                        :value="item.statusCd">{{item.name}}
                                </option>
                            </select>
                        </div>
                        <div class="col-sm-4">
                            <select class="custom-select"
                                    v-model="inspectionTaskDetailManageInfo.conditions.patrolType">
                                <option selected value="">{{vc.i18n('请选择巡检情况','inspectionTaskDetails')}}</option>
                                <option v-for="(item,index) in inspectionTaskDetailManageInfo.patrolTypes" :key="index"
                                        :value="item.statusCd">{{item.name}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>
                        <span><vc:i18n name="巡检明细信息" namespace="inspectionTaskDetails"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;">
                        <button type="button" class="btn btn-primary btn-sm" v-on:click="_exportExcel()">
                            <i class="fa fa-plus"></i>
                            <span><vc:i18n name="导出" namespace="inspectionTaskDetails"></vc:i18n></span>
                        </button>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="table-responsive inspection-detail-table-wrapper">
                        <table class="footable table table-stripped toggle-arrow-tiny inspection-detail-table-2" data-page-size="15">
                        <thead>
                        <tr>
                            <th class="text-center">
                                <span><vc:i18n name="任务详情ID" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检计划名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检路线名称" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检人" namespace="inspectionTaskDetails"></vc:i18n></span><br/>
                                <span><vc:i18n name="开始/结束时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点" namespace="inspectionTaskDetails"></vc:i18n></span><br/>
                                <span><vc:i18n name="开始/结束时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际巡检时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际签到状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="计划巡检人" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际巡检人" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检方式" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="任务状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检点状态" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center" style="width: 200px;">
                                <span><vc:i18n name="巡检情况" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检照片" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="创建时间" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>
                            <!--<th class="text-center" style="width: 70px;">
                                <span><vc:i18n name="位置信息" namespace="inspectionTaskDetails"></vc:i18n></span>
                            </th>-->
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="inspectionTask in inspectionTaskDetailManageInfo.inspectionTasks">
                            <td class="text-center">{{inspectionTask.taskDetailId}}</td>
                            <td class="text-center">{{inspectionTask.inspectionName}}</td>
                            <td class="text-center">{{inspectionTask.inspectionPlanName}}</td>
                            <td class="text-center">{{inspectionTask.routeName}}</td>
                            <td class="text-center">{{inspectionTask.planInsTime}}<br/>{{inspectionTask.planEndTime}}
                            </td>
                            <td class="text-center">{{inspectionTask.pointStartTime}}<br/>{{inspectionTask.pointEndTime}}
                            </td>
                            <td class="text-center">{{inspectionTask.inspectionTime ? inspectionTask.inspectionTime :
                                '-'}}
                            </td>
                            <td class="text-center text-primary" v-if="inspectionTask.inspectionState == '60000'">
                                {{inspectionTask.inspectionStateName ? inspectionTask.inspectionStateName : '-'}}
                            </td>
                            <td class="text-center text-danger font-bold" v-else>{{inspectionTask.inspectionStateName ?
                                inspectionTask.inspectionStateName : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.planUserName}}</td>
                            <td class="text-center">{{inspectionTask.actUserName ? inspectionTask.actUserName : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.signTypeName}}</td>
                            <td class="text-center">{{inspectionTask.taskStateName}}</td>
                            <td class="text-center text-danger font-bold" v-if="inspectionTask.state == '20200408'">
                                {{inspectionTask.stateName}}
                            </td>
                            <td class="text-center" v-else>{{inspectionTask.stateName}}</td>
                            <!-- <td class="text-center text-danger font-bold" v-if="inspectionTask.patrolType == '20002'">{{inspectionTask.patrolTypeName ? inspectionTask.patrolTypeName : '-'}}</td>
                            <td class="text-center text-primary" v-else>{{inspectionTask.patrolTypeName ? inspectionTask.patrolTypeName : '-'}}</td> -->
                            <td class="text-center text-primary" style="word-break: break-all">
                                {{inspectionTask.description ? inspectionTask.description : '-'}}
                            </td>
                            <td class="text-center" style="white-space: nowrap;">
                                    <span v-for="_photo in inspectionTask.photos">
                                    <img style="width: 60px; height: 60px;" v-bind:src="_photo.url"
                                         v-on:click="openFile(_photo)"/>
                                </span>
                            </td>
                            <td class="text-center">{{inspectionTask.createTime}}</td>
                            <!--<td class="text-center">
                                <button class="btn btn-info"
                                        v-on:click="openMap(inspectionTask.latitude, inspectionTask.longitude)">
                                    <span><vc:i18n name="查看" namespace="inspectionTaskDetails"></vc:i18n></span>
                                </button>
                            </td>-->
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="17">
                                <ul class="pagination float-right"></ul>
                            </td>
                        </tr>
                        </tfoot>
                    </table>
                    </div>
                    <!-- 分页 -->
                    <vc:create path="frame/pagination"></vc:create>
                </div>
            </div>
        </div>
    </div>
    <vc:create path="common/viewMap"></vc:create>
    <vc:create path="common/viewImage"></vc:create>
</div>

<style>
/* 巡检明细表格2样式优化 */
.inspection-detail-table-wrapper {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

.inspection-detail-table-2 {
    min-width: 1200px; /* 设置表格最小宽度 */
    white-space: nowrap;
}

.inspection-detail-table-2 th,
.inspection-detail-table-2 td {
    min-width: 80px; /* 设置最小列宽 */
    text-align: center;
}

/* 特殊列宽度设置 */
.inspection-detail-table-2 th:nth-child(1),
.inspection-detail-table-2 td:nth-child(1) {
    min-width: 120px; /* 任务详情ID */
}

.inspection-detail-table-2 th:nth-child(2),
.inspection-detail-table-2 td:nth-child(2) {
    min-width: 120px; /* 巡检点名称 */
}

.inspection-detail-table-2 th:nth-child(3),
.inspection-detail-table-2 td:nth-child(3) {
    min-width: 150px; /* 巡检计划名称 */
}

.inspection-detail-table-2 th:nth-child(5),
.inspection-detail-table-2 td:nth-child(5) {
    min-width: 180px; /* 巡检人开始/结束时间 */
}

.inspection-detail-table-2 th:nth-child(6),
.inspection-detail-table-2 td:nth-child(6) {
    min-width: 180px; /* 巡检点开始/结束时间 */
}

.inspection-detail-table-2 th:nth-child(10),
.inspection-detail-table-2 td:nth-child(10) {
    min-width: 200px; /* 巡检情况 */
}

.inspection-detail-table-2 th:nth-child(11),
.inspection-detail-table-2 td:nth-child(11) {
    min-width: 120px; /* 巡检照片 */
}

/* 巡检照片样式 */
.inspection-detail-table-2 img {
    width: 50px;
    height: 50px;
    border-radius: 4px;
    cursor: pointer;
    transition: transform 0.2s ease;
    margin: 2px;
}

.inspection-detail-table-2 img:hover {
    transform: scale(1.1);
}

/* 响应式处理 */
@media (max-width: 768px) {
    .inspection-detail-table-2 {
        font-size: 12px;
    }

    .inspection-detail-table-2 img {
        width: 40px;
        height: 40px;
    }
}
</style>
