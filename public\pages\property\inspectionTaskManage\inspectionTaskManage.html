<div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox ">
                <div class="ibox-title">
                    <h5>
                        <span><vc:i18n name="查询条件" namespace="inspectionTaskManage"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;">
                        <button type="button" class="btn btn-link btn-sm" style="margin-right:10px;"
                                v-on:click="_moreCondition()">
                            {{inspectionTaskManageInfo.moreCondition == true?'隐藏':'更多'}}
                        </button>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入执行人','inspectionTaskManage')"
                                       v-model="inspectionTaskManageInfo.conditions.planUserName" class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检开始时间','inspectionTaskManage')"
                                       v-model="inspectionTaskManageInfo.conditions.startTime"
                                       class=" form-control startTime">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入实际巡检结束时间','inspectionTaskManage')"
                                       v-model="inspectionTaskManageInfo.conditions.endTime"
                                       class=" form-control endTime">
                            </div>
                        </div>
                        <div class="col-sm-2">
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_queryInspectionTaskMethod()">
                                <i class="fa fa-search"></i>
                                <span><vc:i18n name="查询" namespace="inspectionTaskManage"></vc:i18n></span>
                            </button>
                            <button type="button" class="btn btn-primary btn-sm"
                                    v-on:click="_resetInspectionTaskMethod()">
                                <i class="fa fa-repeat"></i>
                                <span><vc:i18n name="重置" namespace="inspectionTaskManage"></vc:i18n></span>
                            </button>
                        </div>
                    </div>
                    <div class="row" v-show="inspectionTaskManageInfo.moreCondition == true">
                        <div class="col-sm-3">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入巡检计划ID','inspectionTaskManage')"
                                       v-model="inspectionTaskManageInfo.conditions.inspectionPlanId"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-4">
                            <div class="form-group">
                                <input type="text" :placeholder="vc.i18n('请输入巡检计划名称','inspectionTaskManage')"
                                       v-model="inspectionTaskManageInfo.conditions.inspectionPlanName"
                                       class=" form-control">
                            </div>
                        </div>
                        <div class="col-sm-3">
                            <select class="custom-select" v-model="inspectionTaskManageInfo.conditions.state">
                                <option selected value="">{{vc.i18n('请选择巡检状态','inspectionTaskManage')}}</option>
                                <option v-for="(item,index) in inspectionTaskManageInfo.stateTypes" :key="index"
                                        :value="item.statusCd">{{item.name}}
                                </option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <div class="ibox">
                <div class="ibox-title">
                    <h5>
                        <span><vc:i18n name="巡检任务" namespace="inspectionTaskManage"></vc:i18n></span>
                    </h5>
                    <div class="ibox-tools" style="top:10px;"></div>
                </div>
                <div class="ibox-content">
                    <el-table
                            :data="inspectionTaskManageInfo.inspectionTasks"
                            style="width: 100%"
                            @sort-change="sortTable"
                            class="inspection-task-table"
                    >
                        <el-table-column
                                prop="taskId"
                                label="任务编码"
                                width="180">
                        </el-table-column>
                        <el-table-column
                                prop="inspectionPlanName"
                                label="巡检计划"
                                width="200">
                        </el-table-column>
                        <el-table-column
                                prop="planInsTime"
                                label="巡检开始/结束时间"
                                width="200"
                                sortable="custom"
                        >
                            <template slot-scope="scope">
                                <span>{{ scope.row.planInsTime }}<br/>{{ scope.row.planEndTime }}</span>
                            </template>
                        </el-table-column>


                        <el-table-column
                                prop="actInsTime"
                                label="实际巡检时间"
                                width="150"
                                sortable="custom"
                        >
                            <template slot-scope="scope">
                                <span style="margin-left: 10px">{{ scope.row.actInsTime ? scope.row.actInsTime : '-' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="originalPlanUserName"
                                label="计划巡检人"
                                width="120"
                        >
                            <template slot-scope="scope">
                                <span style="margin-left: 10px">{{ scope.row.originalPlanUserName ? scope.row.originalPlanUserName : '-' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="planUserName"
                                label="当前巡检人"
                                width="120"
                        >
                        </el-table-column>

                        <el-table-column
                                prop="transferDesc"
                                label="转移描述"
                                width="150"
                        >
                            <template slot-scope="scope">
                                <span style="margin-left: 10px">{{ scope.row.transferDesc ? scope.row.transferDesc : '-' }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="signTypeName"
                                label="巡检方式"
                                width="100"
                        >
                        </el-table-column>

                        <el-table-column
                                prop="stateName"
                                label="巡检状态"
                                width="100"
                        >
                            <template slot-scope="scope">
                                <span v-if="scope.row.stateName == '20200408'" class="text-center text-danger font-bold">{{ scope.row.stateName }}</span>
                                <span v-else class="text-center">{{ scope.row.stateName }}</span>
                            </template>
                        </el-table-column>

                        <el-table-column
                                prop="inspectionPlanPeriodName"
                                label="操作"
                                width="180"
                                fixed="right"
                        >
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.state == '20200406' || scope.row.state == '20200405'" @click="_openInspectionTaskTransfer(scope.row)" type="text" size="small">流转</el-button>
                                <el-button  @click="_openInspectionTaskDetail(scope.row)" type="text" size="small">详情</el-button>
                                <el-button v-if="vc.hasPrivilege('502022031612550001')" @click="_openDeleteInspectionTask(scope.row)" type="text" size="small">删除</el-button>
                            </template>
                        </el-table-column>

                    </el-table>
                    <!--<table class="footable table table-stripped toggle-arrow-tiny" data-page-size="15">
                        <thead>
                        <tr>
                            <th class="text-center">
                                <span><vc:i18n name="任务编码" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检计划" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检人" namespace="inspectionTaskManage"></vc:i18n></span><br/>
                                <span><vc:i18n name="开始/结束时间" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="实际巡检时间" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="计划巡检人" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="当前巡检人" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="转移描述" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检方式" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="巡检状态" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                            <th class="text-center">
                                <span><vc:i18n name="操作" namespace="inspectionTaskManage"></vc:i18n></span>
                            </th>
                        </tr>
                        </thead>
                        <tbody>
                        <tr v-for="inspectionTask in inspectionTaskManageInfo.inspectionTasks">
                            <td class="text-center">{{inspectionTask.taskId}}</td>
                            <td class="text-center">{{inspectionTask.inspectionPlanName}}</td>
                            <td class="text-center">{{inspectionTask.planInsTime}}<br/>{{inspectionTask.planEndTime}}
                            </td>
                            <td class="text-center">{{inspectionTask.actInsTime ? inspectionTask.actInsTime : '-'}}</td>
                            <td class="text-center">
                                {{inspectionTask.originalPlanUserName ? inspectionTask.originalPlanUserName : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.planUserName}}</td>
                            <td class="text-center">
                                {{inspectionTask.transferDesc ? inspectionTask.transferDesc : '-'}}
                            </td>
                            <td class="text-center">{{inspectionTask.signTypeName}}</td>
                            <td class="text-center text-danger font-bold" v-if="inspectionTask.state == '20200408'">
                                {{inspectionTask.stateName}}
                            </td>
                            <td class="text-center" v-else>{{inspectionTask.stateName}}</td>
                            <td class="text-center">
                                <div class="btn-group">
                                    <button class="btn-white btn btn-xs"
                                            v-on:click="_openInspectionTaskTransfer(inspectionTask)"
                                            v-if="inspectionTask.state == '20200406' || inspectionTask.state == '20200405'">
                                        <span><vc:i18n name="流转" namespace="inspectionTaskManage"></vc:i18n></span>
                                    </button>
                                    <button class="btn-white btn btn-xs"
                                            v-on:click="_openInspectionTaskDetail(inspectionTask)">
                                        <span><vc:i18n name="详情" namespace="inspectionTaskManage"></vc:i18n></span>
                                    </button>
                                    <button class="btn-white btn btn-xs" v-if="vc.hasPrivilege('502022031612550001')"
                                            v-on:click="_openDeleteInspectionTask(inspectionTask)">
                                        <span><vc:i18n name="删除" namespace="inspectionTaskManage"></vc:i18n></span>
                                    </button>
                                </div>
                            </td>
                        </tr>
                        </tbody>
                        <tfoot>
                        <tr>
                            <td colspan="11">
                                <ul class="pagination float-right"></ul>
                            </td>
                        </tr>
                        </tfoot>
                    </table>-->
                    <!-- 分页 -->
                    <div class="row margin-top-xs">
                        <div class="col-sm-9">
                            <div>
                                巡检任务是根据巡检计划，每天晚上2点钟自动生成，如果没有生成 请确认是否开启了定时任务，或者巡检计划是否设置正确
                            </div>
                        </div>
                        <div class="col-sm-3 float-right">
                            <vc:create path="frame/pagination"></vc:create>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <vc:create path="property/inspectionTaskDetail"></vc:create>
    <vc:create path="property/inspectionTaskTransfer"></vc:create>
    <vc:create path="property/deleteInspectionTask"></vc:create>
</div>
